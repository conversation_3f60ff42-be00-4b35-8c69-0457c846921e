<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <style>
    body {
      width: 300px;
      padding: 20px;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
      margin: 0;
    }
    
    .header {
      text-align: center;
      margin-bottom: 20px;
    }
    
    .header h2 {
      margin: 0;
      font-size: 18px;
      font-weight: 600;
    }
    
    .capture-btn {
      width: 100%;
      padding: 15px;
      background: rgba(255, 255, 255, 0.2);
      border: 2px solid rgba(255, 255, 255, 0.3);
      border-radius: 10px;
      color: white;
      font-size: 16px;
      font-weight: 600;
      cursor: pointer;
      transition: all 0.3s ease;
      margin-bottom: 15px;
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 10px;
    }
    
    .capture-btn:hover {
      background: rgba(255, 255, 255, 0.3);
      border-color: rgba(255, 255, 255, 0.5);
      transform: translateY(-2px);
    }
    
    .capture-btn:active {
      transform: translateY(0);
    }
    
    .capture-btn.capturing {
      background: rgba(76, 175, 80, 0.3);
      border-color: rgba(76, 175, 80, 0.5);
      cursor: not-allowed;
    }
    
    .status {
      text-align: center;
      font-size: 14px;
      margin-top: 10px;
      opacity: 0.8;
    }
    
    .shortcut-info {
      background: rgba(0, 0, 0, 0.2);
      padding: 10px;
      border-radius: 8px;
      font-size: 12px;
      text-align: center;
      margin-top: 15px;
    }
    
    .icon {
      font-size: 20px;
    }
  </style>
</head>
<body>
  <div class="header">
    <h2>📸 Daakia Screen Capture</h2>
  </div>
  
  <button id="captureBtn" class="capture-btn">
    <span class="icon">📸</span>
    <span>Take Screenshot</span>
  </button>
  
  <div id="status" class="status">Ready to capture</div>
  
  <div class="shortcut-info">
    <strong>Global Shortcut:</strong><br>
    <span id="shortcutKey">Ctrl+Shift+S</span>
  </div>
  
  <script src="popup.js"></script>
</body>
</html>
