// Background script - handles global shortcuts
chrome.commands.onCommand.addListener(async (command) => {
  if (command === "capture-screen") {
    console.log("Global screen capture shortcut triggered!");
    
    try {
      // Get all tabs
      const tabs = await chrome.tabs.query({});
      
      // Find the Daakia meeting tab
      const meetingTab = tabs.find(tab => 
        tab.url && (
          tab.url.includes('daakia') || 
          tab.url.includes('localhost') ||
          tab.url.includes('your-meeting-domain.com') // Replace with your actual domain
        )
      );
      
      if (meetingTab) {
        // Send message to the meeting tab to trigger screenshot
        chrome.tabs.sendMessage(meetingTab.id, {
          action: "TRIGGER_SCREEN_CAPTURE"
        }).catch(err => {
          console.log("Could not send message to meeting tab:", err);
        });
        
        // Optional: Switch to the meeting tab briefly
        // chrome.tabs.update(meetingTab.id, { active: true });
      } else {
        console.log("No Daakia meeting tab found");
      }
    } catch (error) {
      console.error("Error in global shortcut:", error);
    }
  }
});

// Listen for tab updates to detect when meeting starts
chrome.tabs.onUpdated.addListener((tabId, changeInfo, tab) => {
  if (changeInfo.status === 'complete' && tab.url && 
      (tab.url.includes('daakia') || tab.url.includes('localhost'))) {
    console.log("Daakia meeting tab detected:", tab.url);
  }
});
