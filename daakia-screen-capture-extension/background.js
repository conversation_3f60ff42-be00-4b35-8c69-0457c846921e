// Background script - handles global shortcuts and popup messages
async function triggerScreenCapture() {
  console.log("Triggering screen capture...");

  try {
    // Get all tabs
    const tabs = await chrome.tabs.query({});

    // Find the Daakia meeting tab
    const meetingTab = tabs.find(tab =>
      tab.url && (
        tab.url.includes('daakia') ||
        tab.url.includes('localhost') ||
        tab.url.includes('your-meeting-domain.com') // Replace with your actual domain
      )
    );

    if (meetingTab) {
      // Send message to the meeting tab to trigger screenshot
      const response = await chrome.tabs.sendMessage(meetingTab.id, {
        action: "TRIGGER_SCREEN_CAPTURE"
      }).catch(err => {
        console.log("Could not send message to meeting tab:", err);
        return { success: false, message: "Could not communicate with meeting tab" };
      });

      return response || { success: true, message: "Screenshot triggered" };
    } else {
      console.log("No Daakia meeting tab found");
      return { success: false, message: "No meeting tab found. Please open Daakia meeting first." };
    }
  } catch (error) {
    console.error("Error triggering capture:", error);
    return { success: false, message: "Extension error: " + error.message };
  }
}

// Handle global keyboard shortcut
chrome.commands.onCommand.addListener(async (command) => {
  if (command === "capture-screen") {
    console.log("Global screen capture shortcut triggered!");
    await triggerScreenCapture();
  }
});

// Handle messages from popup
chrome.runtime.onMessage.addListener(async (message, _, sendResponse) => {
  if (message.action === "TRIGGER_CAPTURE_FROM_POPUP") {
    console.log("Screen capture triggered from popup!");
    const result = await triggerScreenCapture();
    sendResponse(result);
    return true; // Keep message channel open for async response
  }
});

// Listen for tab updates to detect when meeting starts
chrome.tabs.onUpdated.addListener((_, changeInfo, tab) => {
  if (changeInfo.status === 'complete' && tab.url &&
      (tab.url.includes('daakia') || tab.url.includes('localhost'))) {
    console.log("Daakia meeting tab detected:", tab.url);
  }
});
