// Popup script - handles the extension popup UI
document.addEventListener('DOMContentLoaded', function() {
  const captureBtn = document.getElementById('captureBtn');
  const status = document.getElementById('status');
  const shortcutKey = document.getElementById('shortcutKey');
  
  // Detect platform and show correct shortcut
  const isMac = navigator.platform.toUpperCase().indexOf('MAC') >= 0;
  shortcutKey.textContent = isMac ? 'Cmd+Shift+S' : 'Ctrl+Shift+S';
  
  // Check if we're on a meeting page
  checkMeetingStatus();
  
  // Handle capture button click
  captureBtn.addEventListener('click', async function() {
    if (captureBtn.classList.contains('capturing')) {
      return; // Prevent multiple clicks
    }
    
    // Update UI to show capturing state
    captureBtn.classList.add('capturing');
    captureBtn.innerHTML = '<span class="icon">⏳</span><span>Capturing...</span>';
    status.textContent = 'Taking screenshot...';
    
    try {
      // Send message to background script to trigger capture
      const response = await chrome.runtime.sendMessage({
        action: 'TRIGGER_CAPTURE_FROM_POPUP'
      });
      
      if (response && response.success) {
        // Success - show feedback and close popup
        captureBtn.innerHTML = '<span class="icon">✅</span><span>Success!</span>';
        status.textContent = 'Screenshot taken!';
        
        // Close popup after 1 second
        setTimeout(() => {
          window.close();
        }, 1000);
      } else {
        // Error - show error message
        captureBtn.classList.remove('capturing');
        captureBtn.innerHTML = '<span class="icon">❌</span><span>Error</span>';
        status.textContent = response?.message || 'No meeting tab found';
        
        // Reset button after 2 seconds
        setTimeout(resetButton, 2000);
      }
    } catch (error) {
      console.error('Error triggering capture:', error);
      captureBtn.classList.remove('capturing');
      captureBtn.innerHTML = '<span class="icon">❌</span><span>Error</span>';
      status.textContent = 'Extension error occurred';
      
      // Reset button after 2 seconds
      setTimeout(resetButton, 2000);
    }
  });
  
  function resetButton() {
    captureBtn.classList.remove('capturing');
    captureBtn.innerHTML = '<span class="icon">📸</span><span>Take Screenshot</span>';
    status.textContent = 'Ready to capture';
  }
  
  async function checkMeetingStatus() {
    try {
      // Check if meeting tab exists
      const tabs = await chrome.tabs.query({});
      const meetingTab = tabs.find(tab => 
        tab.url && (
          tab.url.includes('daakia') || 
          tab.url.includes('localhost')
        )
      );
      
      if (meetingTab) {
        status.textContent = 'Meeting detected - Ready!';
        captureBtn.style.background = 'rgba(76, 175, 80, 0.3)';
        captureBtn.style.borderColor = 'rgba(76, 175, 80, 0.5)';
      } else {
        status.textContent = 'No meeting found';
        captureBtn.style.background = 'rgba(255, 152, 0, 0.3)';
        captureBtn.style.borderColor = 'rgba(255, 152, 0, 0.5)';
      }
    } catch (error) {
      console.error('Error checking meeting status:', error);
    }
  }
});
