// Content script - runs on all pages, listens for messages from background
console.log("Daakia Screen Capture extension loaded");

// Listen for messages from background script
chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
  if (message.action === "TRIGGER_SCREEN_CAPTURE") {
    console.log("Received screen capture trigger from extension");
    
    // Check if we're on a Daakia meeting page
    const isDaakiaPage = window.location.href.includes('daakia') || 
                        window.location.href.includes('localhost') ||
                        window.location.href.includes('your-meeting-domain'); // Replace with your domain
    
    if (isDaakiaPage) {
      // Try to find and trigger the screen capture button
      const captureButton = document.querySelector('.screen-capture-button');
      
      if (captureButton) {
        console.log("Found screen capture button, clicking it!");
        captureButton.click();
        sendResponse({ success: true, message: "Screen capture triggered" });
      } else {
        // Alternative: Dispatch a custom event that your React component can listen to
        console.log("Screen capture button not found, dispatching custom event");
        window.dispatchEvent(new CustomEvent('globalScreenCapture', {
          detail: { source: 'extension' }
        }));
        sendResponse({ success: true, message: "Custom event dispatched" });
      }
    } else {
      console.log("Not on Daakia meeting page, ignoring");
      sendResponse({ success: false, message: "Not on meeting page" });
    }
  }
  
  return true; // Keep message channel open for async response
});

// Optional: Add visual indicator when extension is active
if (window.location.href.includes('daakia') || window.location.href.includes('localhost')) {
  // Add a small indicator that the extension is active
  const indicator = document.createElement('div');
  indicator.id = 'daakia-extension-indicator';
  indicator.innerHTML = '🎯 Global Capture Ready';
  indicator.style.cssText = `
    position: fixed;
    top: 10px;
    right: 10px;
    background: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 5px 10px;
    border-radius: 5px;
    font-size: 12px;
    z-index: 10000;
    opacity: 0.7;
    pointer-events: none;
  `;
  
  // Add indicator after page loads
  setTimeout(() => {
    if (document.body) {
      document.body.appendChild(indicator);
      
      // Hide after 3 seconds
      setTimeout(() => {
        if (indicator.parentNode) {
          indicator.parentNode.removeChild(indicator);
        }
      }, 3000);
    }
  }, 1000);
}
