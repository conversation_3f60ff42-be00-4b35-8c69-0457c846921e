/*eslint-disable*/
import React, { useState, useEffect } from 'react';
import moment from 'moment';
import { DataReceivedEvent } from '../../../../utils/constants';
import { ReactComponent as CaptureIcon } from '../../../../assets/icons/Copy.svg';
import { SaasService } from '../../../services/saasServices';
import { useSaasHelpers } from '../../../helpers/helpers';
import './ScreenCaptureButton.scss';

export function ScreenCaptureButton({
  room,
  screenShareTracks,
  // focusTrack,
  setToastNotification,
  setToastStatus,
  setShowToast,
  setShowPopover
}) {
  const [isCapturing, setIsCapturing] = useState(false);
  const [shouldDownload, setShouldDownload] = useState(true); // State to control download
  const { saasHostToken } = useSaasHelpers();

  const uploadScreenshot = async (formData) => {
    try {
      const response = await SaasService.uploadScreenshot(
        formData,
        { "Content-Type": "multipart/form-data" },
        saasHostToken
      );
      if (response.success === 0) {
        console.log("Error uploading screenshot", response);
        throw new Error("Failed to upload screenshot");
      } else if (response.success === 1) {
        console.log("Screenshot uploaded successfully", response);
      }
      return response;
    } catch (error) {
      console.error("Screenshot upload error:", error);
      throw new Error(`Screenshot upload failed: ${error.message}`);
    }
  };



  const captureGridLayout = async () => {
    try {
      // Find the grid layout container
      const gridLayoutWrapper = document.querySelector('.lk-grid-layout-wrapper');
      if (!gridLayoutWrapper) {
        throw new Error("Grid layout not found - make sure you're in grid view");
      }

      console.log("Found grid layout:", gridLayoutWrapper);

      // Simple approach: Use getDisplayMedia to capture the current tab
      // This preserves all CSS styling, animations, and visual effects
      try {
        const stream = await navigator.mediaDevices.getDisplayMedia({
          video: {
            mediaSource: 'screen'
          },
          audio: false
        });

        // Create video element to capture the stream
        const video = document.createElement('video');
        video.srcObject = stream;
        video.autoplay = true;
        video.muted = true;
        video.playsInline = true;

        // Wait for video to be ready
        await new Promise((resolve, reject) => {
          video.onloadedmetadata = () => {
            video.play().then(resolve).catch(reject);
          };
          video.onerror = reject;
        });

        // Wait a bit for the video to stabilize
        await new Promise(resolve => setTimeout(resolve, 500));

        // Create canvas and capture the video frame
        const canvas = document.createElement('canvas');
        const ctx = canvas.getContext('2d');

        canvas.width = video.videoWidth;
        canvas.height = video.videoHeight;

        ctx.drawImage(video, 0, 0);

        // Stop the stream
        stream.getTracks().forEach(track => track.stop());
        video.srcObject = null;

        console.log("Grid layout captured via screen capture, dimensions:", canvas.width, "x", canvas.height);
        return canvas;

      } catch (screenCaptureError) {
        console.warn("Screen capture failed, falling back to simple method:", screenCaptureError);

        // Fallback: Just capture the videos in the grid
        return await captureGridVideosOnly();
      }

    } catch (error) {
      console.error("Grid layout capture error:", error);
      throw new Error(`Grid capture failed: ${error.message}`);
    }
  };

  const captureGridVideosOnly = async () => {
    // Simple fallback: just capture the videos
    const gridLayoutWrapper = document.querySelector('.lk-grid-layout-wrapper');
    const videos = gridLayoutWrapper.querySelectorAll('video');

    if (videos.length === 0) {
      throw new Error("No videos found in grid layout");
    }

    // Use the first video as the main capture
    const mainVideo = videos[0];
    if (mainVideo.videoWidth === 0 || mainVideo.videoHeight === 0) {
      throw new Error("Video not ready for capture");
    }

    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');

    canvas.width = mainVideo.videoWidth;
    canvas.height = mainVideo.videoHeight;

    ctx.drawImage(mainVideo, 0, 0);

    console.log("Fallback: captured main video, dimensions:", canvas.width, "x", canvas.height);
    return canvas;
  };

  const captureFocusedScreenShare = async () => {
    try {
      const screenShareVideo = document.querySelector('video[data-lk-source="screen_share"]');

      if (!screenShareVideo) {
        throw new Error("Screen share video not found - make sure screen sharing is active");
      }

      if (screenShareVideo.videoWidth === 0 || screenShareVideo.videoHeight === 0) {
        throw new Error("Screen share video has no dimensions - video may not be loaded yet");
      }

      console.log("Found screen share video:", screenShareVideo);
      console.log("Video dimensions:", screenShareVideo.videoWidth, "x", screenShareVideo.videoHeight);
      console.log("Video filter:", screenShareVideo.style.filter);

      // Create canvas to capture the screen share video AS IT APPEARS (with blur if blurred)
      const canvas = document.createElement('canvas');
      const ctx = canvas.getContext('2d');

      // Set canvas dimensions to match video's actual dimensions
      canvas.width = screenShareVideo.videoWidth;
      canvas.height = screenShareVideo.videoHeight;

      // Draw the current video frame to canvas exactly as it appears
      ctx.drawImage(screenShareVideo, 0, 0, canvas.width, canvas.height);

      return canvas;

    } catch (error) {
      console.error("Focused screen share capture error:", error);
      throw new Error(`Focused capture failed: ${error.message}`);
    }
  };

  const processCanvasAndSave = async (canvas, captureType) => {
    return new Promise((resolve, reject) => {
      canvas.toBlob((blob) => {
        if (!blob) {
          reject(new Error(`Failed to create blob from ${captureType} canvas`));
          return;
        }

        const formData = new FormData();
        const filename = `${room?.name}-${captureType}-${moment().format('YYYY-MM-DD-HH-mm-ss')}.png`;
        const file = new File([blob], filename, { type: 'image/png' });
        formData.append('image', file);
        if (room?.roomInfo?.name) {
          formData.append('meeting_uid', room.roomInfo.name);
        }

        // Upload to API
        uploadScreenshot(formData);

        // Download locally only if shouldDownload is true
        if (shouldDownload) {
          const url = URL.createObjectURL(blob);
          const link = document.createElement('a');
          link.download = filename;
          link.href = url;
          document.body.appendChild(link);
          link.click();
          document.body.removeChild(link);
          URL.revokeObjectURL(url);
        }

        resolve();
      }, 'image/png', 0.95);
    });
  };

  const broadcastCaptureEvent = async () => {
    if (!room || !room.localParticipant) return;

    const encoder = new TextEncoder();
    const data = encoder.encode(
      JSON.stringify({
        action: DataReceivedEvent.SCREEN_CAPTURE_TAKEN,
        participantName: room.localParticipant.name || room.localParticipant.identity,
        timestamp: Date.now(),
      })
    );

    await room.localParticipant.publishData(data, {
      reliable: true,
    });
  };

  const handleScreenCapture = async () => {
    if (isCapturing) return;

    setIsCapturing(true);

    try {
      let canvas;
      let captureType;

      // Determine capture mode based on screen sharing status
      if (screenShareTracks && screenShareTracks.length > 0) {
        // Mode 1: Screen Share Mode - Capture focused screen share
        console.log("Screen share detected - capturing focused screen share");
        canvas = await captureFocusedScreenShare();
        captureType = "screen-share";
      } else {
        // Mode 2: Normal Mode - Capture grid layout
        console.log("No screen share - capturing grid layout");
        canvas = await captureGridLayout();
        captureType = "grid-layout";
      }

      // Process and save the canvas
      await processCanvasAndSave(canvas, captureType);

      // Broadcast to all participants
      await broadcastCaptureEvent();

      // Show success toast
      const message = captureType === "screen-share"
        ? "Screen share captured successfully"
        : "Grid layout captured successfully";
      setToastNotification(message);
      setToastStatus("success");
      setShowToast(true);

      // Close the popover
      if (setShowPopover) {
        setShowPopover(false);
      }

    } catch (error) {
      console.error("Screen capture failed:", error);
      setToastNotification(`Screen capture failed: ${error.message}`);
      setToastStatus("error");
      setShowToast(true);
    } finally {
      setIsCapturing(false);
    }
  };

  useEffect(() => {

    const handleKeyDown = (event) => {
      if (event.shiftKey && event.key.toLowerCase() === 's') {
        if (screenShareTracks && screenShareTracks.length > 0 && !isCapturing) {
          event.preventDefault(); 
          handleScreenCapture();
        }
      }
    };

    const handleKeyUp = () => {
      // No need for key tracking with simple Shift + S
    };

    // Add event listeners
    document.addEventListener('keydown', handleKeyDown);
    document.addEventListener('keyup', handleKeyUp);

    // Cleanup event listeners on unmount
    return () => {
      document.removeEventListener('keydown', handleKeyDown);
      document.removeEventListener('keyup', handleKeyUp);
    };
  }, [screenShareTracks, isCapturing]); // Dependencies to ensure latest state

  // Always render - button adapts to screen share mode or grid mode

  return (
    <div
      onClick={handleScreenCapture}
      className={`screen-capture-button ${isCapturing ? 'capturing' : ''}`}
    >
      <CaptureIcon
        style={{ color: isCapturing ? '#1890ff' : '#fff' }}
      />
    </div>
  );
}
