/*eslint-disable*/
import React, { useState, useEffect } from 'react';
import moment from 'moment';
import { DataReceivedEvent } from '../../../../utils/constants';
import { ReactComponent as CaptureIcon } from '../../../../assets/icons/Copy.svg';
import { SaasService } from '../../../services/saasServices';
import { useSaasHelpers } from '../../../helpers/helpers';
import './ScreenCaptureButton.scss';

export function ScreenCaptureButton({
  room,
  screenShareTracks,
  // focusTrack,
  setToastNotification,
  setToastStatus,
  setShowToast,
  setShowPopover
}) {
  const [isCapturing, setIsCapturing] = useState(false);
  const [shouldDownload, setShouldDownload] = useState(true); // State to control download
  const { saasHostToken } = useSaasHelpers();

  const uploadScreenshot = async (formData) => {
    try {
      const response = await SaasService.uploadScreenshot(
        formData,
        { "Content-Type": "multipart/form-data" },
        saasHostToken
      );
      if (response.success === 0) {
        console.log("Error uploading screenshot", response);
        throw new Error("Failed to upload screenshot");
      } else if (response.success === 1) {
        console.log("Screenshot uploaded successfully", response);
      }
      return response;
    } catch (error) {
      console.error("Screenshot upload error:", error);
      throw new Error(`Screenshot upload failed: ${error.message}`);
    }
  };



  const captureGridLayout = async () => {
    try {
      // Find the grid layout container
      const gridLayoutWrapper = document.querySelector('.lk-grid-layout-wrapper');
      if (!gridLayoutWrapper) {
        throw new Error("Grid layout not found - make sure you're in grid view");
      }

      console.log("Found grid layout:", gridLayoutWrapper);

      // Use html2canvas to capture the entire grid layout
      const html2canvas = await import('html2canvas');
      const canvas = await html2canvas.default(gridLayoutWrapper, {
        useCORS: true,
        allowTaint: true,
        backgroundColor: '#000000',
        scale: 1,
        logging: false
      });

      console.log("Grid layout captured, dimensions:", canvas.width, "x", canvas.height);
      return canvas;

    } catch (error) {
      console.error("Grid layout capture error:", error);
      throw new Error(`Grid capture failed: ${error.message}`);
    }
  };

  const captureFocusedScreenShare = async () => {
    try {
      const screenShareVideo = document.querySelector('video[data-lk-source="screen_share"]');

      if (!screenShareVideo) {
        throw new Error("Screen share video not found - make sure screen sharing is active");
      }

      if (screenShareVideo.videoWidth === 0 || screenShareVideo.videoHeight === 0) {
        throw new Error("Screen share video has no dimensions - video may not be loaded yet");
      }

      console.log("Found screen share video:", screenShareVideo);
      console.log("Video dimensions:", screenShareVideo.videoWidth, "x", screenShareVideo.videoHeight);
      console.log("Video filter:", screenShareVideo.style.filter);

      // Create canvas to capture the screen share video AS IT APPEARS (with blur if blurred)
      const canvas = document.createElement('canvas');
      const ctx = canvas.getContext('2d');

      // Set canvas dimensions to match video's actual dimensions
      canvas.width = screenShareVideo.videoWidth;
      canvas.height = screenShareVideo.videoHeight;

      // Draw the current video frame to canvas exactly as it appears
      ctx.drawImage(screenShareVideo, 0, 0, canvas.width, canvas.height);

      return canvas;

    } catch (error) {
      console.error("Focused screen share capture error:", error);
      throw new Error(`Focused capture failed: ${error.message}`);
    }
  };

  const processCanvasAndSave = async (canvas, captureType) => {
    return new Promise((resolve, reject) => {
      canvas.toBlob((blob) => {
        if (!blob) {
          reject(new Error(`Failed to create blob from ${captureType} canvas`));
          return;
        }

        const formData = new FormData();
        const filename = `${room?.name}-${captureType}-${moment().format('YYYY-MM-DD-HH-mm-ss')}.png`;
        const file = new File([blob], filename, { type: 'image/png' });
        formData.append('image', file);
        if (room?.roomInfo?.name) {
          formData.append('meeting_uid', room.roomInfo.name);
        }

        // Upload to API
        uploadScreenshot(formData);

        // Download locally only if shouldDownload is true
        if (shouldDownload) {
          const url = URL.createObjectURL(blob);
          const link = document.createElement('a');
          link.download = filename;
          link.href = url;
          document.body.appendChild(link);
          link.click();
          document.body.removeChild(link);
          URL.revokeObjectURL(url);
        }

        resolve();
      }, 'image/png', 0.95);
    });
  };

  const broadcastCaptureEvent = async () => {
    if (!room || !room.localParticipant) return;

    const encoder = new TextEncoder();
    const data = encoder.encode(
      JSON.stringify({
        action: DataReceivedEvent.SCREEN_CAPTURE_TAKEN,
        participantName: room.localParticipant.name || room.localParticipant.identity,
        timestamp: Date.now(),
      })
    );

    await room.localParticipant.publishData(data, {
      reliable: true,
    });
  };

  const handleScreenCapture = async () => {
    if (isCapturing) return;

    setIsCapturing(true);

    try {
      let canvas;
      let captureType;

      // Determine capture mode based on screen sharing status
      if (screenShareTracks && screenShareTracks.length > 0) {
        // Mode 1: Screen Share Mode - Capture focused screen share
        console.log("Screen share detected - capturing focused screen share");
        canvas = await captureFocusedScreenShare();
        captureType = "screen-share";
      } else {
        // Mode 2: Normal Mode - Capture grid layout
        console.log("No screen share - capturing grid layout");
        canvas = await captureGridLayout();
        captureType = "grid-layout";
      }

      // Process and save the canvas
      await processCanvasAndSave(canvas, captureType);

      // Broadcast to all participants
      await broadcastCaptureEvent();

      // Show success toast
      const message = captureType === "screen-share"
        ? "Screen share captured successfully"
        : "Grid layout captured successfully";
      setToastNotification(message);
      setToastStatus("success");
      setShowToast(true);

      // Close the popover
      if (setShowPopover) {
        setShowPopover(false);
      }

    } catch (error) {
      console.error("Screen capture failed:", error);
      setToastNotification(`Screen capture failed: ${error.message}`);
      setToastStatus("error");
      setShowToast(true);
    } finally {
      setIsCapturing(false);
    }
  };

  useEffect(() => {

    const handleKeyDown = (event) => {
      if (event.shiftKey && event.key.toLowerCase() === 's') {
        if (screenShareTracks && screenShareTracks.length > 0 && !isCapturing) {
          event.preventDefault(); 
          handleScreenCapture();
        }
      }
    };

    const handleKeyUp = () => {
      // No need for key tracking with simple Shift + S
    };

    // Add event listeners
    document.addEventListener('keydown', handleKeyDown);
    document.addEventListener('keyup', handleKeyUp);

    // Cleanup event listeners on unmount
    return () => {
      document.removeEventListener('keydown', handleKeyDown);
      document.removeEventListener('keyup', handleKeyUp);
    };
  }, [screenShareTracks, isCapturing]); // Dependencies to ensure latest state

  // Only render if screen sharing is active
  if (!screenShareTracks || screenShareTracks.length === 0) {
    return null;
  }

  return (
    <div
      onClick={handleScreenCapture}
      className={`screen-capture-button ${isCapturing ? 'capturing' : ''}`}
    >
      <CaptureIcon
        style={{ color: isCapturing ? '#1890ff' : '#fff' }}
      />
    </div>
  );
}
